# Debian VPS 防火墙一键配置工具

## 🛡️ 脚本作用

### 安全防护功能
- **阻止陌生IP SSH登录** - 只允许指定IP地址SSH访问服务器
- **防止DDoS攻击** - 默认拒绝所有未授权的入站连接
- **限制端口访问** - 只开放必要的服务端口
- **自动系统准备** - 自动安装iptables并处理冲突

### 一键完成
1. **系统环境准备**: 自动安装iptables、禁用ufw冲突
2. **SSH访问控制**: 只有 `.env` 中配置的IP才能SSH登录
3. **端口白名单**: 只开放 `.env` 中指定的端口给全球访问
4. **开机自启**: 重启后自动恢复防火墙配置

## 📋 配置文件说明




## 🚀 部署操作指导
```
# 1. 上传文件
scp firewall.sh .env root@VPS_IP:/var/srv/iptables/
cd /var/srv/iptables/

# 2. 修改配置,注意当前ip地址在允许访问⭐️
nano .env

# 3. 一键执行
cd /var/srv/iptables/
chmod +x firewall.sh && ./firewall.sh

- ✅ 安装iptables相关包
- ✅ 禁用ufw冲突
- ✅ 配置防火墙规则
- ✅ 设置开机自启

5. 查看配置后iptables规则
iptables -L -n -v

6. 测试SSH访问 和 测试开放的端口

7. 查看当前的一些信息
查看当前连接 netstat -tulnp
查看服务状态 systemctl status iptables-restore.service
查看备份文件 ls -la /root/iptables_backup_*.txt

8. 修改配置信息重启
修改配置文件	nano .env
重新应用配置	./firewall.sh

9. 如果配置错误导致无法连接，可以通过VPS控制台执行:
方法1: 清除所有规则
iptables -F
iptables -P INPUT ACCEPT
iptables -P OUTPUT ACCEPT
iptables -P FORWARD ACCEPT
方法2: 恢复备份
iptables-restore < /root/iptables_backup_最新时间戳.txt
方法3: 停止防火墙服务
systemctl stop iptables-restore.service



```

```

## ⚠️ 重要注意事项

### 执行前必须检查
1. **确认当前IP**: 确保你的IP在 `ALLOWED_IPS` 中
2. **备份重要**: 脚本会自动备份，但建议手动记录配置
3. **测试环境**: 建议先在测试服务器上验证

### 安全提醒
- 执行后立即测试SSH连接
- 保留VPS控制台访问权限以备紧急恢复
- 定期更新允许的IP地址
- 只开放必要的服务端口

### 常见问题
**Q: 执行后无法SSH连接？**
A: 通过VPS控制台执行 `iptables -F && iptables -P INPUT ACCEPT`

**Q: 服务无法访问？**  
A: 检查端口是否在 `PUBLIC_PORTS` 中，检查服务是否正常运行

**Q: 如何添加新的IP？**
A: 修改 `.env` 文件中的 `ALLOWED_IPS`，重新执行 `./firewall.sh`

## 📊 配置效果

执行成功后，您的Debian VPS将具备：
- ✅ **系统环境就绪** (自动安装iptables并处理冲突)
- ✅ **SSH访问安全** (只允许指定IP)
- ✅ **服务端口开放** (按需配置)
- ✅ **DDoS防护** (拒绝未授权连接)
- ✅ **出站正常** (不影响服务器访问外网)
- ✅ **开机自启** (重启后自动恢复配置)
- ✅ **sing-box兼容** (为后续服务部署做好准备)



